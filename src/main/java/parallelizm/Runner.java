package parallelizm;


public class Runner {

    public static void main(String[] args) {

        Mouth mouth = new Mouth();

        Thread thread_A = new Thread(() -> {
            try {
                mouth.first();
            } catch (InterruptedException ignored) {
            }
        });
        Thread thread_B = new Thread(() -> {
            try {
                mouth.second();
            } catch (InterruptedException ignored) {
            }
        });
        Thread thread_C = new Thread(() -> {
            try {
                mouth.third();
            } catch (InterruptedException ignored) {
            }
        });
        thread_A.start();
        thread_B.start();
        thread_C.start();
    }
}
