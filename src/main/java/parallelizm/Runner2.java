package parallelizm;

import lombok.SneakyThrows;

import java.util.concurrent.Semaphore;

public class Runner2 {
    @SneakyThrows
    public static void main(String[] args) {
        Semaphore semaphore = new Semaphore(0);
        semaphore.release();
        var b = semaphore.availablePermits();
        var a = semaphore.tryAcquire();
        System.out.println(a);
        System.out.println(b);
    }

}
