package parallelizm;

public class Mouth {
    // Declare synchronization objects as attributes
    private final Object firstLock;
    private final Object secondLock;
    private volatile boolean firstDone;
    private volatile boolean secondDone;

    public Mouth() {
        // Initialize synchronization objects in constructor
        this.firstLock = new Object();
        this.secondLock = new Object();
        this.firstDone = false;
        this.secondDone = false;
    }

    // Update the method
    public void first() throws InterruptedException {
        System.out.print("I "); // Do not change or remove this line
        synchronized (firstLock) {
            firstDone = true;
            firstLock.notifyAll();
        }
    }

    // Update the method
    public void second() throws InterruptedException {
        synchronized (firstLock) {
            while (!firstDone) {
                firstLock.wait();
            }
        }
        System.out.print("love "); // Do not change or remove this line
        synchronized (secondLock) {
            secondDone = true;
            secondLock.notifyAll();
        }
    }

    // Update the method
    public void third() throws InterruptedException {
        synchronized (secondLock) {
            while (!secondDone) {
                secondLock.wait();
            }
        }
        System.out.print("programming!"); // Do not change or remove this line
    }
}