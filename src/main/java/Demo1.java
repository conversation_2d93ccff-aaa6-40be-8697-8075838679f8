import java.util.concurrent.*;

public class Demo1 {
    public static void main(String[] args) throws InterruptedException {
        ExecutorService pool = Executors.newFixedThreadPool(3); // максимум 3 параллельно
        CountDownLatch latch = new CountDownLatch(10);

        for (int i = 1; i <= 10; i++) {
            int id = i;
            pool.submit(() -> {
                System.out.printf("Задача %02d старт в %s%n", id, Thread.currentThread().getName());
                try {
                    Thread.sleep(500 + (id % 3) * 300);
                } catch (InterruptedException ignored) {
                }
                System.out.printf("Задача %02d финиш в %s%n", id, Thread.currentThread().getName());
                latch.countDown();
            });
        }

        pool.shutdown();           // новых задач не будет
        latch.await();             // ждём завершения всех 10
        System.out.println("Все задачи завершены");
    }
}
