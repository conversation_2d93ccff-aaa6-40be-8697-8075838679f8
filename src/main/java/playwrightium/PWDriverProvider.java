package playwrightium;

import com.codeborne.selenide.WebDriverProvider;
import org.brit.driver.PlaywrightiumDriver;
import org.brit.options.PlaywrightiumOptions;
import org.openqa.selenium.*;


public class PWDriverProvider implements WebDriverProvider {

    @Override
    public WebDriver createDriver(Capabilities caps) {
        PlaywrightiumOptions options = createPlaywrightiumOptions();

        if (caps != null) {
            options.merge(caps);
        }

        return new PlaywrightiumDriver(options);
    }

    private PlaywrightiumOptions createPlaywrightiumOptions() {
        PlaywrightiumOptions options = new PlaywrightiumOptions();
        options.setHeadless(false);
        options.setBrowserName("chromium");

        return options;
    }
}