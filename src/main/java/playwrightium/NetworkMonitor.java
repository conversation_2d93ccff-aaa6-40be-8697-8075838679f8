package playwrightium;

import org.openqa.selenium.WebDriver;
import org.openqa.selenium.bidi.HasBiDi;
import org.openqa.selenium.bidi.module.Network;
import org.openqa.selenium.bidi.network.BeforeRequestSent;
import org.openqa.selenium.bidi.network.ResponseDetails;
import org.openqa.selenium.bidi.network.Header;

import java.io.Closeable;
import java.io.IOException;
import java.time.Instant;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Predicate;

public class NetworkMonitor implements Closeable {
    public static class Event {
        public final long ts = System.currentTimeMillis();
        public final String phase;      // request | response
        public final String method;
        public final String url;
        public final Integer status;    // только для response
        public final List<Header> reqHeaders;
        public final List<Header> resHeaders;

        Event(String phase, String method, String url,
              Integer status, List<Header> reqHeaders, List<Header> resHeaders) {
            this.phase = phase;
            this.method = method;
            this.url = url;
            this.status = status;
            this.reqHeaders = reqHeaders;
            this.resHeaders = resHeaders;
        }

        @Override
        public String toString() {
            return "%s | %s %s | %s".formatted(
                    Instant.ofEpochMilli(ts), method, url, status == null ? "" : status);
        }
    }

    private final Network net;
    private final List<Event> events = new CopyOnWriteArrayList<>();
    private final AtomicBoolean hasHttpErrors = new AtomicBoolean(false);
    private final Predicate<String> urlFilter;

    private NetworkMonitor(WebDriver driver, Predicate<String> urlFilter) {
        this.net = new Network(driver);
        this.urlFilter = urlFilter != null ? urlFilter : (u -> true);
        attach();
    }

    /**
     * Запустить монитор (должен вызываться ПОСЛЕ open("about:blank") и до навигации).
     */
    public static NetworkMonitor start(WebDriver driver, Predicate<String> urlFilter) {
        if (!(driver instanceof HasBiDi)) {
            throw new IllegalStateException("WebDriver без BiDi (нужно webSocketUrl=true и свежий Chrome/Driver).");
        }
        return new NetworkMonitor(driver, urlFilter);
    }

    private void attach() {
        net.onBeforeRequestSent((BeforeRequestSent e) -> {
            String url = e.getRequest().getUrl();
            if (!urlFilter.test(url)) return;
            events.add(new Event(
                    "request",
                    e.getRequest().getMethod(),
                    url,
                    null,
                    e.getRequest().getHeaders(),
                    null
            ));
        });

        net.onResponseCompleted((ResponseDetails e) -> {
            String url = e.getRequest().getUrl();
            if (!urlFilter.test(url)) return;
            int status = e.getResponseData().getStatus();
            events.add(new Event(
                    "response",
                    e.getRequest().getMethod(),
                    url,
                    status,
                    null,
                    e.getResponseData().getHeaders()
            ));
            if (status >= 400) hasHttpErrors.set(true);
        });
    }

    /**
     * Все собранные события (потокобезопасный список).
     */
    public List<Event> getEvents() {
        return events;
    }

    /**
     * Быстрая эвристика: это XHR/fetch? (по заголовкам и исключая статику).
     */
    public static boolean isLikelyXHR(List<Header> headers, String url) {
        if (url.matches(".*\\.(?:js|css|png|jpg|jpeg|gif|svg|ico|woff2?|ttf|eot)(\\?.*)?$")) return false;
        String dest = header(headers, "sec-fetch-dest");
        if ("empty".equalsIgnoreCase(dest)) return true;
        String xr = header(headers, "x-requested-with");
        return "xmlhttprequest".equalsIgnoreCase(xr);
    }

    private static String header(List<Header> hs, String name) {
        if (hs == null) return null;
        for (Header h : hs) {
            if (name.equalsIgnoreCase(h.getName())) return String.valueOf(h.getValue());
        }
        return null;
    }

    /**
     * Ассерт: нет HTTP 4xx/5xx (по отфильтрованным URL).
     */
    public void assertNoHttpErrors() {
        if (hasHttpErrors.get()) {
            StringBuilder sb = new StringBuilder("Обнаружены HTTP 4xx/5xx:\n");
            for (Event e : events) {
                if ("response".equals(e.phase) && e.status != null && e.status >= 400) {
                    sb.append(e.status).append(" ").append(e.method).append(" ").append(e.url).append("\n");
                }
            }
            throw new AssertionError(sb.toString());
        }
    }

    @Override
    public void close() throws IOException {
        net.close();
    }
}