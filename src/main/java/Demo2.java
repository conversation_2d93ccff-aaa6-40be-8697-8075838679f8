public class Demo2 {
    static class Counter {
        private int value = 0;

        // ВАРИАНТ А (ломаный): без синхронизации
        void incBroken() {
            value++;
        }

        // ВАРИАНТ B (починка): синхронизированный
        synchronized void incFixed() {
            value++;
        }

        int get() {
            return value;
        }
    }

    public static void main(String[] args) throws InterruptedException {
        testBroken();
        testFixed();
    }

    static void testBroken() throws InterruptedException {
        Counter c = new Counter();
        Thread t1 = new Thread(() -> {
            for (int i = 0; i < 100_000; i++) c.incBroken();
        });
        Thread t2 = new Thread(() -> {
            for (int i = 0; i < 100_000; i++) c.incBroken();
        });
        t1.start();
        t2.start();
        t1.join();
        t2.join();
        System.out.println("Без синхронизации: ожидали 200000, получили " + c.get());
    }

    static void testFixed() throws InterruptedException {
        Counter c = new Counter();
        Thread t1 = new Thread(() -> {
            for (int i = 0; i < 100_000; i++) c.incFixed();
        });
        Thread t2 = new Thread(() -> {
            for (int i = 0; i < 100_000; i++) c.incFixed();
        });
        t1.start();
        t2.start();
        t1.join();
        t2.join();
        System.out.println("С synchronized: ожидали 200000, получили " + c.get());
    }
}
