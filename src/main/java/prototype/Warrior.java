package prototype;

import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
public class Warrior extends Character implements Cloneable {

    public Warrior(String type, String name, int age) {
        super(type, name, age);
    }

    @Override
    void specialAbility() {
        System.out.println("Being as a warrior");
    }

    @Override
    public Warrior clone() {
        try {
            return (Warrior) super.clone();
        } catch (CloneNotSupportedException e) {
            throw new AssertionError();
        }
    }
}
