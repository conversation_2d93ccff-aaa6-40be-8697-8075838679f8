package prototype;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@ToString
@AllArgsConstructor
public abstract class Character {

    private String type;
    private String name;
    private int age;

    @Override
    public Character clone() throws CloneNotSupportedException {
        return (Character) super.clone();
    }

    abstract void specialAbility();

    public Character setName(String name) {
        this.name = name;
        return this;
    }

    public Character setAge(int age) {
        this.age = age;
        return this;
    }
}
