package prototype;

import lombok.SneakyThrows;


public class Factory {


    public static Character createCharacter(String type) {

        String defaultName = "Unknown";
        int defaultAge = 0;

        return switch (type) {
            case "Wizard" -> new Wizard(type, defaultName, defaultAge);
            case "Warrior" -> new Warrior(type, defaultName, defaultAge);
            default -> throw new IllegalArgumentException("Invalid type");
        };

    }

    @SneakyThrows
    public static Character cloneCharacter(Character character) {
        return character.clone();
    }
}
