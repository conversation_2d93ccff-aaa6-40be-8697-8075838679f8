package prototype;


import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
public class Wizard extends Character implements Cloneable {

    public Wizard(String type, String name, int age) {
        super(type, name, age);
    }

    @Override
    void specialAbility() {
        System.out.println("Being as a wizard");
    }

    @Override
    public Wizard clone() {
        try {
            return (Wizard) super.clone();
        } catch (CloneNotSupportedException e) {
            throw new AssertionError();
        }
    }
}
