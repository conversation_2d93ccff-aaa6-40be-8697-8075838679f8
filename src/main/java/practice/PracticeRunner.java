package practice;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

public class PracticeRunner {
    private static final Logger log = LoggerFactory.getLogger(PracticeRunner.class);

    public static void main(String[] args) {


        var a = List.of(new People("Name1", 1), new People("Name2", 2), new People("Name3", 3));

        var b = Map.of(
                12, new People("Name1", 1),
                23, new People("Name2", 2),
                33, new People("Name3", 3));

        b.forEach((key, value) -> {
            System.out.println(key);
        });

//        Stream.of(1,2,3,4,5).gather()

        int c = 10;
        var d = IntStream.range(0, c)
                .boxed()
                .collect(Collectors.toMap(Function.identity(), index -> new People("Name" + index, index)));

        d.forEach((key, value) -> {
            System.out.println(key + " " + value);
        });

        log.info("Number - {}", 5);
        log.trace("Trace level");
        log.debug("Debug level");
        log.info("Info level");
        log.warn("Warn level");
        log.error("Error level");
    }
}
