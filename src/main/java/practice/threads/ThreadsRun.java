package practice.threads;

public class ThreadsRun {
    public static void main(String[] args) {
        ThreadsExample threadsExample1 = new ThreadsExample();
        ThreadsExample threadsExample2 = new ThreadsExample();
        Thread thread1 = new Thread(() -> System.out.println("Hi - " + Thread.currentThread().getName()), "t1");
        Thread thread2 = new Thread(() -> System.out.println("Hi - " + Thread.currentThread().getName()), "t2");
        Thread thread3 = new Thread(() -> System.out.println("Hi - " + Thread.currentThread().getName()), "t3");
        thread1.start();
        thread2.start();
        thread3.start();
    }
}
