package practice.stringPractice;

import java.util.HashMap;
import java.util.Map;

public class RunnerString {
    public static void main(String[] args) {
        String s = "asdfjhabsdfabajhsdfbajsdaaadsasjdhfb";


//        Arrays.stream(s.split("")).distinct().forEach(System.out::println);

        Map<Character, Integer> map = new HashMap<>();

        for (int i = 0; i < s.length(); i++) {
            var currentElement = s.charAt(i);
            if (!map.containsKey(currentElement)) {
                map.put(currentElement, 1);
            } else {
                map.put(currentElement, map.get(currentElement) + 1);
            }
        }

        // Сортируем записи карты по значению (по убыванию)
        map.entrySet().stream()
                .sorted(Map.Entry.<Character, Integer>comparingByValue().reversed())
                .forEach(System.out::println);


    }
}
