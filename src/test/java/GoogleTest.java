import com.codeborne.selenide.Configuration;
import com.codeborne.selenide.Selenide;
import com.codeborne.selenide.WebDriverRunner;
import lombok.SneakyThrows;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.bidi.HasBiDi;
import org.openqa.selenium.bidi.log.ConsoleLogEntry;
import org.openqa.selenium.bidi.log.JavascriptLogEntry;
import org.openqa.selenium.bidi.module.LogInspector;
import org.openqa.selenium.chrome.ChromeOptions;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;
import playwrightium.NetworkMonitor;
import playwrightium.PWDriverProvider;

import static com.codeborne.selenide.Condition.text;
import static com.codeborne.selenide.Condition.visible;
import static com.codeborne.selenide.Selenide.*;

public class GoogleTest {
    @Test
    public void testSelenoidConnection() {
        Configuration.remote = "http://***************:4444/wd/hub";
        Configuration.browser = "chrome";

        // Простейший тест
        open("https://www.google.com");
        System.out.println("✅ Соединение с Selenoid работает!");
        Selenide.sleep(5000);
    }

    @BeforeTest
    public void setUp() {
        Configuration.browser = PWDriverProvider.class.getName();
        Configuration.pageLoadTimeout = -1; // Отключаем pageLoadTimeout
    }

    @Test
    public void testPlaywrightDevNavigation() {
        // Переход на страницу
        open("https://www.playwright.dev");

        // Проверка загрузки страницы
        $("h1").shouldHave(text("Playwright"));

        // Взаимодействие с элементами
        $("[href='/docs/intro']").click();

        // Проверка перехода
        $("h1").shouldHave(text("Installation"));

        // Скриншот для отчета
        Selenide.screenshot("playwright_docs_page");
    }

    @Test
    public void testPlaywrightDevNavigation2() {
        // Используйте правильный URL
        Configuration.remote = "http://***************:2222/wd/hub";
        Configuration.browser = "chrome";
        Configuration.browserVersion = "latest";
        Configuration.browserSize = "1920x1080";

        open("https://www.playwright.dev");

        // Добавьте проверки
        $("h1").shouldHave(text("Playwright"));
        Selenide.sleep(10000);
    }

    @Test
    void testPlaywrightDevNavigation3() {
        ChromeOptions options = new ChromeOptions();
        options.setCapability("webSocketUrl", true); // включает BiDi для Chrome

        Configuration.browser = "chrome";                 // <— ОБЯЗАТЕЛЬНО
        Configuration.browserCapabilities = options;
        Configuration.browserSize = "1920x1080";
        Configuration.timeout = 8000;

        open("https://www.playwright.dev");
        $("h1").shouldBe(visible).shouldHave(text("Playwright"));

        WebDriver driver = WebDriverRunner.getWebDriver();

        // защитная проверка: драйвер действительно с BiDi
        if (!(driver instanceof HasBiDi)) {
            throw new IllegalStateException("Текущий WebDriver не поддерживает BiDi. " +
                    "Проверь Chrome/ChromeDriver и конфиг.");
        }

        try (LogInspector logs = new LogInspector(driver)) {
            StringBuilder jsErrors = new StringBuilder();

            logs.onConsoleEntry((ConsoleLogEntry e) ->
                    System.out.println("console." + e.getMethod() + ": " + e.getText()));

            logs.onJavaScriptException((JavascriptLogEntry e) ->
                    jsErrors.append(e.getText()).append("\n"));

            executeJavaScript("console.log('bidi: ping')");
            $("h1").shouldBe(visible);

            if (jsErrors.length() > 0) {
                throw new AssertionError("JS-ошибки:\n" + jsErrors);
            }
        }
    }

    @SneakyThrows
    @Test
    void test3() {
        ChromeOptions options = new ChromeOptions();
        options.setCapability("webSocketUrl", true); // включает BiDi для Chrome

        Configuration.browser = "chrome";                 // <— ОБЯЗАТЕЛЬНО
        Configuration.browserCapabilities = options;
        Configuration.browserSize = "1920x1080";
        Configuration.timeout = 8000;
        open("about:blank");
        WebDriver driver = WebDriverRunner.getWebDriver();
        driver.navigate().to("https://www.playwright.dev");
        $("h1").shouldBe(visible).shouldHave(text("Playwright"));


        // Фильтр по домену (можно u -> true для всех)
        var filter = (java.util.function.Predicate<String>) (u -> u.startsWith("https://playwright.dev"));

        try (NetworkMonitor mon = NetworkMonitor.start(driver, filter)) {
            // --- UI-шаги идут как обычно, монитор слушает «на фоне» ---
            driver.navigate().to("https://playwright.dev/");
            $("h1").shouldBe(visible).shouldHave(text("Playwright"));

            // пример: посмотреть только XHR-вызовы
            mon.getEvents().stream()
                    .filter(e -> "request".equals(e.phase) && NetworkMonitor.isLikelyXHR(e.reqHeaders, e.url))
                    .forEach(e -> System.out.println("[XHR] " + e.method + " " + e.url));

            // при необходимости — фейлим на 4xx/5xx
            mon.assertNoHttpErrors();
        }


    }
}
