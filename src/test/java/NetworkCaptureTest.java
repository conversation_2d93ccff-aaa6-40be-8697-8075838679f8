
import com.codeborne.selenide.Configuration;
import com.codeborne.selenide.WebDriverRunner;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.bidi.HasBiDi;
import org.openqa.selenium.bidi.module.Network;
import org.openqa.selenium.bidi.network.BeforeRequestSent;
import org.openqa.selenium.bidi.network.ResponseDetails;
import org.openqa.selenium.bidi.network.Header;
import org.openqa.selenium.chrome.ChromeOptions;
import org.testng.annotations.Test;

import java.nio.file.Files;
import java.nio.file.Path;
import java.time.Instant;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicBoolean;

import static com.codeborne.selenide.Condition.text;
import static com.codeborne.selenide.Condition.visible;
import static com.codeborne.selenide.Selenide.*;

public class NetworkCaptureTest {

    // Модель события
    static class NetEntry {
        String phase;               // request | response-start | response-done | error
        String method;
        String url;
        Integer status;             // для ответа
        String statusText;          // для ответа
        List<Header> reqHeaders;    // <-- List<Header>
        List<Header> resHeaders;    // <-- List<Header>
        String errorText;           // для сетевых ошибок
        long ts = System.currentTimeMillis();

        @Override
        public String toString() {
            return "%s | %s %s | %s %s".formatted(
                    Instant.ofEpochMilli(ts),
                    method != null ? method : "-",
                    url,
                    status != null ? status : "",
                    statusText != null ? statusText : ""
            );
        }
    }

    @Test
    public void captureAllRequestsAndResponses() throws Exception {
        // 1) Включаем BiDi
        ChromeOptions options = new ChromeOptions();
        options.setCapability("webSocketUrl", true);

        Configuration.browser = "chrome";
        Configuration.browserCapabilities = options;
        Configuration.browserSize = "1920x1080";
        Configuration.timeout = 8000;

        // 2) Драйвер и подписки ДО навигации
        open("about:blank");
        WebDriver driver = WebDriverRunner.getWebDriver();
        if (!(driver instanceof HasBiDi)) {
            throw new IllegalStateException("WebDriver без BiDi (нужен webSocketUrl=true и свежий Chrome/Driver).");
        }

        List<NetEntry> all = new CopyOnWriteArrayList<>();
        AtomicBoolean hasHttpErrors = new AtomicBoolean(false);
        StringBuilder badCalls = new StringBuilder();

        try (Network net = new Network(driver)) {
            final String DOMAIN = "https://playwright.dev"; // убери фильтр, если нужны все домены

            net.onBeforeRequestSent((BeforeRequestSent e) -> {
                String url = e.getRequest().getUrl();
                if (!url.startsWith(DOMAIN)) return;

                var d = new NetEntry();
                d.phase = "request";
                d.method = e.getRequest().getMethod();
                d.url = url;
                d.reqHeaders = e.getRequest().getHeaders(); // List<Header>
                all.add(d);
                System.out.println("[REQ] " + d);
            });

            net.onResponseStarted((ResponseDetails e) -> {
                String url = e.getRequest().getUrl();
                if (!url.startsWith(DOMAIN)) return;

                var d = new NetEntry();
                d.phase = "response-start";
                d.method = e.getRequest().getMethod();
                d.url = url;
                d.status = e.getResponseData().getStatus();
                d.statusText = e.getResponseData().getStatusText();
                d.resHeaders = e.getResponseData().getHeaders(); // List<Header>
                all.add(d);
                System.out.println("[RES-START] " + d);
            });

            net.onResponseCompleted((ResponseDetails e) -> {
                String url = e.getRequest().getUrl();
                if (!url.startsWith(DOMAIN)) return;

                var d = new NetEntry();
                d.phase = "response-done";
                d.method = e.getRequest().getMethod();
                d.url = url;
                d.status = e.getResponseData().getStatus();
                d.statusText = e.getResponseData().getStatusText();
                d.resHeaders = e.getResponseData().getHeaders(); // List<Header>
                all.add(d);
                System.out.println("[RES-DONE] " + d);

                if (d.status != null && d.status >= 400) {
                    hasHttpErrors.set(true);
                    badCalls.append(d.status).append(" ").append(d.url).append("\n");
                }
            });

            net.onFetchError(err -> {
                String url = err.getRequest().getUrl();
                if (!url.startsWith(DOMAIN)) return;

                var d = new NetEntry();
                d.phase = "error";
                d.method = err.getRequest().getMethod();
                d.url = url;
                d.errorText = err.getErrorText();
                all.add(d);
                System.out.println("[ERR] " + d.errorText + " @ " + d.url);

                hasHttpErrors.set(true);
                badCalls.append("FETCH ERROR ").append(d.errorText).append(" @ ").append(d.url).append("\n");
            });

            // 3) Навигация и базовая проверка
            driver.navigate().to("https://playwright.dev/");
            $("h1").shouldBe(visible).shouldHave(text("Playwright"));

            // 4) Сериализация в JSONL (заголовки как массив объектов {name,value})
            Path out = Path.of("build", "network-log.jsonl");
            Files.createDirectories(out.getParent());
            var lines = all.stream().map(NetworkCaptureTest::toJson).toList();
            Files.write(out, lines);
            System.out.println("Saved network log to: " + out.toAbsolutePath());

            if (hasHttpErrors.get()) {
                throw new AssertionError("Обнаружены проблемные сетевые ответы/ошибки:\n" + badCalls);
            }
        }
    }

    @Test
    void log_all_requests_and_responses() {
        // 1) Включаем BiDi для Chrome
        ChromeOptions options = new ChromeOptions();
        options.setCapability("webSocketUrl", true);
        Configuration.browser = "chrome";
        Configuration.browserCapabilities = options;

        // 2) Создаём драйвер и ВЕШАЕМ слушатели ДО навигации
        open("about:blank");
        WebDriver driver = WebDriverRunner.getWebDriver();
        if (!(driver instanceof HasBiDi)) {
            throw new IllegalStateException("WebDriver без BiDi (нужен webSocketUrl=true и свежий Chrome/Driver).");
        }

        try (Network net = new Network(driver)) {
            // Если хочешь только свой домен — раскомментируй фильтр
            // final String DOMAIN = "https://playwright.dev";

            net.onBeforeRequestSent((BeforeRequestSent e) -> {
                String method = e.getRequest().getMethod();
                String url = e.getRequest().getUrl();
                // if (!url.startsWith(DOMAIN)) return;
                System.out.println("[REQ] " + method + " " + url);
            });

            net.onResponseCompleted((ResponseDetails e) -> {
                String method = e.getRequest().getMethod();
                String url = e.getRequest().getUrl();
                int status = e.getResponseData().getStatus();
                // if (!url.startsWith(DOMAIN)) return;
                System.out.println("[RES] " + status + " <- " + method + " " + url);
            });

            // 3) Навигация и базовая проверка
            driver.navigate().to("https://playwright.dev/");
            $("h1").shouldBe(visible).shouldHave(text("Playwright"));
        }
    }


    // сериализация с headers как массив объектов
    private static String toJson(NetEntry e) {
        String q = "\"";
        java.util.function.Function<String, String> esc = s -> s == null ? null : s.replace("\"", "\\\"");
        return "{"
                + q + "ts" + q + ":" + e.ts + ","
                + q + "phase" + q + ":" + q + esc.apply(e.phase) + q + ","
                + q + "method" + q + ":" + (e.method == null ? "null" : q + esc.apply(e.method) + q) + ","
                + q + "url" + q + ":" + (e.url == null ? "null" : q + esc.apply(e.url) + q) + ","
                + q + "status" + q + ":" + (e.status == null ? "null" : e.status) + ","
                + q + "statusText" + q + ":" + (e.statusText == null ? "null" : q + esc.apply(e.statusText) + q) + ","
                + q + "reqHeaders" + q + ":" + headersToJson(e.reqHeaders) + ","
                + q + "resHeaders" + q + ":" + headersToJson(e.resHeaders) + ","
                + q + "errorText" + q + ":" + (e.errorText == null ? "null" : q + esc.apply(e.errorText) + q)
                + "}";
    }

    private static String headersToJson(List<Header> hs) {
        if (hs == null || hs.isEmpty()) return "[]";
        StringBuilder sb = new StringBuilder("[");
        for (int i = 0; i < hs.size(); i++) {
            Header h = hs.get(i);
            String name = escapeJson(String.valueOf(h.getName()));
            String value = escapeJson(String.valueOf(h.getValue())); // <-- ключевая правка

            sb.append("{\"name\":\"").append(name)
                    .append("\",\"value\":\"").append(value)
                    .append("\"}");

            if (i < hs.size() - 1) sb.append(",");
        }
        sb.append("]"); // <-- была ошибка со скобкой
        return sb.toString();
    }

    private static String escapeJson(String s) {
        if (s == null) return "";
        // минимально достаточное экранирование для JSON-строк
        return s
                .replace("\\", "\\\\")
                .replace("\"", "\\\"")
                .replace("\n", "\\n")
                .replace("\r", "\\r")
                .replace("\t", "\\t");
    }

    @Test
    void log_only_xhr() {
        ChromeOptions options = new ChromeOptions();
        options.setCapability("webSocketUrl", true);
        Configuration.browser = "chrome";
        Configuration.browserCapabilities = options;

        open("about:blank");
        WebDriver driver = WebDriverRunner.getWebDriver();
        if (!(driver instanceof HasBiDi)) {
            throw new IllegalStateException("WebDriver без BiDi");
        }

        try (Network net = new Network(driver)) {
            // Если нужен только свой домен:
            // final String DOMAIN = "https://playwright.dev";

            net.onBeforeRequestSent((BeforeRequestSent e) -> {
                String url = e.getRequest().getUrl();
                String method = e.getRequest().getMethod();
                List<Header> reqHs = e.getRequest().getHeaders();
                // if (!url.startsWith(DOMAIN)) return;
                if (isLikelyXHR(reqHs, url)) {
                    System.out.println("[XHR-REQ] " + method + " " + url);
                }
            });

            net.onResponseCompleted((ResponseDetails e) -> {
                String url = e.getRequest().getUrl();
                String method = e.getRequest().getMethod();
                int status = e.getResponseData().getStatus();
                List<Header> resHs = e.getResponseData().getHeaders();
                // if (!url.startsWith(DOMAIN)) return;
                if (isLikelyXHRByResponse(resHs, url)) {
                    System.out.println("[XHR-RES] " + status + " <- " + method + " " + url);
                }
            });

            driver.navigate().to("https://playwright.dev/");
            $("h1").shouldBe(visible).shouldHave(text("Playwright"));
        }
    }

    /**
     * Эвристика для XHR/fetch на основании заголовков запроса + URL
     */
    private static boolean isLikelyXHR(List<Header> reqHeaders, String url) {
        if (isStaticAsset(url)) return false;

        String xr = getHeader(reqHeaders, "x-requested-with");
        if ("xmlhttprequest".equalsIgnoreCase(xr)) return true;

        String dest = getHeader(reqHeaders, "sec-fetch-dest");
        if ("empty".equalsIgnoreCase(dest)) {
            // часто у fetch/XHR; скрипты/JS тоже могут быть empty — отсекаем по расширению выше
            return true;
        }
        return false;
    }

    /**
     * Эвристика по ответу: контент JSON/XML и т.п.
     */
    private static boolean isLikelyXHRByResponse(List<Header> resHeaders, String url) {
        if (isStaticAsset(url)) return false;
        String ct = getHeader(resHeaders, "content-type");
        if (ct == null) return false;
        ct = ct.toLowerCase(Locale.ROOT);
        return ct.contains("application/json")
                || ct.contains("application/xml")
                || ct.contains("text/json")
                || ct.contains("text/xml");
    }

    private static String getHeader(List<Header> hs, String name) {
        if (hs == null) return null;
        for (Header h : hs) {
            if (name.equalsIgnoreCase(h.getName())) {
                return String.valueOf(h.getValue());
            }
        }
        return null;
    }

    private static boolean isStaticAsset(String url) {
        return url.matches(".*\\.(?:js|css|png|jpg|jpeg|gif|svg|ico|woff2?|ttf|eot)(\\?.*)?$");
    }

}
